import rosbag
import numpy as np
from sensor_msgs.msg import PointCloud2, Image
import sensor_msgs.point_cloud2 as pc2
from cv_bridge import CvBridge
import cv2
import open3d as o3d

def create_colored_pointcloud(depth_msg, color_msg, depth_intrinsics, color_intrinsics):
    """将深度点云和彩色图像结合创建彩色点云"""
    bridge = CvBridge()
    
    # 转换彩色图像
    color_image = bridge.imgmsg_to_cv2(color_msg, "bgr8")
    color_image = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
    
    # 读取点云
    points = pc2.read_points(depth_msg, field_names=("x", "y", "z"), skip_nans=True)
    points_array = np.array(list(points))
    
    if points_array.size > 0:
        # 创建Open3D点云
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points_array)
        
        # 这里需要相机内参来做投影，暂时用深度着色
        z_values = points_array[:, 2]
        colors = plt.cm.viridis((z_values - z_values.min()) / (z_values.max() - z_values.min()))[:, :3]
        pcd.colors = o3d.utility.Vector3dVector(colors)
        
        return pcd, color_image
    
    return None, color_image

# 使用示例
bag = rosbag.Bag('my_recording.bag')
# ... 读取同步的深度和彩色数据 ...
pcd, color_img = create_colored_pointcloud(depth_msg, color_msg, None, None)