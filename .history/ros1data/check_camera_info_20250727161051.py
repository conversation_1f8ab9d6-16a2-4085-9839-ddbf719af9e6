import rosbag
import numpy as np

bag = rosbag.Bag('my_recording.bag')

for topic, msg, t in bag.read_messages(topics=['/camera/color/camera_info']):
    print(f"彩色相机分辨率: {msg.width} x {msg.height}")
    print(f"内参矩阵K: {np.array(msg.K).reshape(3, 3)}")
    print(f"畸变参数D: {msg.D}")
    break

for topic, msg, t in bag.read_messages(topics=['/camera/depth/camera_info']):
    print(f"深度相机分辨率: {msg.width} x {msg.height}")
    print(f"内参矩阵K: {np.array(msg.K).reshape(3, 3)}")
    break

bag.close()