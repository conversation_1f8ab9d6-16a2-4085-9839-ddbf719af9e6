# 尝试读取RGB信息
points_with_rgb = pc2.read_points(depth_msg, field_names=("x", "y", "z", "rgb"), skip_nans=True)
points_rgb_array = np.array(list(points_with_rgb))

if points_rgb_array.shape[1] == 4:  # 有RGB信息
    xyz = points_rgb_array[:, :3]
    rgb = points_rgb_array[:, 3]
    
    # 解析RGB值
    rgb_int = rgb.astype(np.uint32)
    r = ((rgb_int >> 16) & 0xFF) / 255.0
    g = ((rgb_int >> 8) & 0xFF) / 255.0
    b = (rgb_int & 0xFF) / 255.0
    colors = np.column_stack([r, g, b])
    
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(xyz)
    pcd.colors = o3d.utility.Vector3dVector(colors)
else:
    print("No RGB information in point cloud")