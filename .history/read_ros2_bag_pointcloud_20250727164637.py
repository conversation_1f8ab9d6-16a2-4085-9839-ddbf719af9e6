#!/usr/bin/env python3
"""
ROS2 bag点云数据读取和可视化脚本
读取指定的ROS2 bag文件中的PointCloud2消息，统计点数并使用Open3D显示
直接读取SQLite数据库，无需rosbag2_py依赖
"""

import numpy as np
import open3d as o3d
import sqlite3
import struct
import sys
import os

def read_pointcloud_from_ros2_bag(bag_path, topic_name="/livox/lidar"):
    """
    从ROS2 bag文件中读取点云数据（直接读取SQLite数据库）

    Args:
        bag_path: ROS2 bag文件夹路径
        topic_name: 点云话题名称

    Returns:
        list: 包含所有点云帧的列表
    """
    # 检查bag文件夹是否存在
    if not os.path.exists(bag_path):
        print(f"错误: bag文件夹不存在: {bag_path}")
        return []

    # 查找.db3文件
    db_files = [f for f in os.listdir(bag_path) if f.endswith('.db3')]
    if not db_files:
        print(f"错误: 在 {bag_path} 中未找到.db3文件")
        return []

    db_file = os.path.join(bag_path, db_files[0])
    print(f"读取数据库文件: {db_file}")

    try:
        # 连接SQLite数据库
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()

        # 查询话题信息
        cursor.execute("SELECT name, type FROM topics")
        topics = cursor.fetchall()

        print("可用话题:")
        topic_id = None
        for topic_info in topics:
            print(f"  - {topic_info[0]}: {topic_info[1]}")
            if topic_info[0] == topic_name:
                # 获取话题ID
                cursor.execute("SELECT id FROM topics WHERE name = ?", (topic_name,))
                result = cursor.fetchone()
                if result:
                    topic_id = result[0]

        if topic_id is None:
            print(f"错误: 未找到话题 {topic_name}")
            conn.close()
            return []

        print(f"\n找到目标话题: {topic_name} (ID: {topic_id})")

        # 查询消息数据
        cursor.execute("""
            SELECT timestamp, data
            FROM messages
            WHERE topic_id = ?
            ORDER BY timestamp
        """, (topic_id,))

        messages = cursor.fetchall()
        conn.close()

        print(f"找到 {len(messages)} 条消息")

        pointclouds = []

        for i, (timestamp, data) in enumerate(messages):
            print(f"处理第 {i+1}/{len(messages)} 条消息...")

            # 解析CDR格式的PointCloud2消息
            points = parse_cdr_pointcloud2(data)

            if points is not None and len(points) > 0:
                pointclouds.append({
                    'points': points,
                    'timestamp': timestamp,
                    'point_count': len(points)
                })
                print(f"  - 点数: {len(points)}")
                print(f"  - 时间戳: {timestamp}")
            else:
                print("  - 警告: 点云为空或解析失败")

        print(f"\n总共处理了 {len(messages)} 条消息，有效点云帧数: {len(pointclouds)}")
        return pointclouds

    except Exception as e:
        print(f"读取数据库时出错: {e}")
        return []

def extract_points_from_pointcloud2(msg):
    """
    从PointCloud2消息中提取点坐标
    
    Args:
        msg: PointCloud2消息
    
    Returns:
        numpy.ndarray: 点坐标数组 (N, 3)
    """
    try:
        # 使用sensor_msgs_py解析点云
        points_list = []
        for point in pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True):
            points_list.append([point[0], point[1], point[2]])
        
        if points_list:
            return np.array(points_list, dtype=np.float32)
        else:
            return None
            
    except Exception as e:
        print(f"解析点云数据时出错: {e}")
        # 备用方法：手动解析
        return extract_points_manual(msg)

def extract_points_manual(msg):
    """
    手动解析PointCloud2消息中的点坐标（备用方法）
    """
    try:
        # 查找x, y, z字段的偏移量
        x_offset = y_offset = z_offset = None
        for field in msg.fields:
            if field.name == 'x':
                x_offset = field.offset
            elif field.name == 'y':
                y_offset = field.offset
            elif field.name == 'z':
                z_offset = field.offset
        
        if x_offset is None or y_offset is None or z_offset is None:
            print("错误: 未找到x, y, z字段")
            return None
        
        # 解析点数据
        points = []
        point_step = msg.point_step
        
        for i in range(0, len(msg.data), point_step):
            if i + point_step <= len(msg.data):
                # 提取x, y, z坐标
                x = struct.unpack('f', msg.data[i + x_offset:i + x_offset + 4])[0]
                y = struct.unpack('f', msg.data[i + y_offset:i + y_offset + 4])[0]
                z = struct.unpack('f', msg.data[i + z_offset:i + z_offset + 4])[0]
                
                # 过滤无效点
                if not (np.isnan(x) or np.isnan(y) or np.isnan(z)):
                    points.append([x, y, z])
        
        return np.array(points, dtype=np.float32) if points else None
        
    except Exception as e:
        print(f"手动解析点云数据时出错: {e}")
        return None

def visualize_pointcloud_with_open3d(points, title="Point Cloud"):
    """
    使用Open3D可视化点云
    
    Args:
        points: 点坐标数组 (N, 3)
        title: 窗口标题
    """
    if points is None or len(points) == 0:
        print("错误: 点云数据为空")
        return
    
    print(f"创建Open3D点云对象，点数: {len(points)}")
    
    # 创建Open3D点云对象
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    
    # 添加颜色（可选）
    # 根据z坐标着色
    z_values = points[:, 2]
    z_min, z_max = z_values.min(), z_values.max()
    if z_max > z_min:
        # 归一化z值到[0,1]
        z_normalized = (z_values - z_min) / (z_max - z_min)
        # 创建颜色映射（蓝色到红色）
        colors = np.zeros((len(points), 3))
        colors[:, 0] = z_normalized  # 红色分量
        colors[:, 2] = 1 - z_normalized  # 蓝色分量
        pcd.colors = o3d.utility.Vector3dVector(colors)
    
    # 创建坐标系
    coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(
        size=1.0,  # 坐标轴长度
        origin=[0, 0, 0]  # 原点位置
    )
    
    # 可视化
    print("启动Open3D可视化...")
    print("操作提示:")
    print("  - 鼠标左键拖拽: 旋转视角")
    print("  - 鼠标右键拖拽: 平移视角") 
    print("  - 滚轮: 缩放")
    print("  - 按 'Q' 或关闭窗口退出")
    
    o3d.visualization.draw_geometries(
        [pcd, coordinate_frame],
        window_name=title,
        width=1024,
        height=768
    )

def main():
    """主函数"""
    # 指定bag文件路径
    bag_path = "ros2_laser/rosbag2_2025_07_25-15_04_42"
    topic_name = "/livox/lidar"
    
    print("=" * 60)
    print("ROS2 Bag点云数据读取和可视化")
    print("=" * 60)
    print(f"Bag文件路径: {bag_path}")
    print(f"目标话题: {topic_name}")
    print()
    
    # 读取点云数据
    pointclouds = read_pointcloud_from_ros2_bag(bag_path, topic_name)
    
    if not pointclouds:
        print("未读取到有效的点云数据")
        return
    
    # 显示统计信息
    print("\n" + "=" * 60)
    print("点云数据统计:")
    print("=" * 60)
    total_points = sum(pc['point_count'] for pc in pointclouds)
    print(f"总帧数: {len(pointclouds)}")
    print(f"总点数: {total_points:,}")
    print(f"平均每帧点数: {total_points // len(pointclouds):,}")
    
    # 显示每帧的详细信息
    for i, pc in enumerate(pointclouds[:5]):  # 只显示前5帧
        print(f"第 {i+1} 帧: {pc['point_count']:,} 点")
    
    if len(pointclouds) > 5:
        print(f"... (还有 {len(pointclouds) - 5} 帧)")
    
    # 可视化第一帧点云
    print(f"\n可视化第一帧点云 (共 {pointclouds[0]['point_count']:,} 个点)...")
    visualize_pointcloud_with_open3d(
        pointclouds[0]['points'], 
        f"ROS2 Point Cloud - Frame 1 ({pointclouds[0]['point_count']:,} points)"
    )
    
    # 询问是否查看更多帧
    if len(pointclouds) > 1:
        while True:
            try:
                choice = input(f"\n是否查看其他帧? (输入帧号 1-{len(pointclouds)}, 或 'q' 退出): ").strip()
                if choice.lower() == 'q':
                    break
                
                frame_idx = int(choice) - 1
                if 0 <= frame_idx < len(pointclouds):
                    print(f"可视化第 {frame_idx + 1} 帧点云...")
                    visualize_pointcloud_with_open3d(
                        pointclouds[frame_idx]['points'],
                        f"ROS2 Point Cloud - Frame {frame_idx + 1} ({pointclouds[frame_idx]['point_count']:,} points)"
                    )
                else:
                    print(f"无效的帧号，请输入 1-{len(pointclouds)}")
            except ValueError:
                print("请输入有效的数字或 'q'")
            except KeyboardInterrupt:
                break
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
