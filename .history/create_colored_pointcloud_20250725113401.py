import rosbag
import numpy as np
from sensor_msgs.msg import PointCloud2, Image, CameraInfo
import sensor_msgs.point_cloud2 as pc2
from cv_bridge import CvBridge
import cv2
import open3d as o3d
import matplotlib.pyplot as plt

def create_colored_pointcloud_with_intrinsics(depth_msg, color_msg, color_camera_info):
    """使用内参将彩色图像投影到点云"""
    bridge = CvBridge()
    
    # 转换彩色图像
    color_image = bridge.imgmsg_to_cv2(color_msg, "bgr8")
    color_image = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
    
    # 读取点云
    points = pc2.read_points(depth_msg, field_names=("x", "y", "z"), skip_nans=True)
    points_array = np.array(list(points))
    
    if points_array.size == 0:
        return None, color_image
    
    # 提取相机内参
    K = np.array(color_camera_info.K).reshape(3, 3)
    fx, fy = K[0, 0], K[1, 1]
    cx, cy = K[0, 2], K[1, 2]
    
    # 将3D点投影到2D图像平面
    x, y, z = points_array[:, 0], points_array[:, 1], points_array[:, 2]
    
    # 投影公式
    u = (fx * x / z + cx).astype(int)
    v = (fy * y / z + cy).astype(int)
    
    # 过滤在图像范围内的点
    h, w = color_image.shape[:2]
    valid_mask = (u >= 0) & (u < w) & (v >= 0) & (v < h) & (z > 0)
    
    valid_points = points_array[valid_mask]
    valid_u = u[valid_mask]
    valid_v = v[valid_mask]
    
    # 从彩色图像中提取对应的颜色
    colors = color_image[valid_v, valid_u] / 255.0
    
    # 稀疏化到4096个点
    if len(valid_points) > 4096:
        indices = np.random.choice(len(valid_points), 4096, replace=False)
        valid_points = valid_points[indices]
        colors = colors[indices]
    
    # 创建彩色点云
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(valid_points)
    pcd.colors = o3d.utility.Vector3dVector(colors)
    
    return pcd, color_image

# 使用示例
bag = rosbag.Bag('my_recording.bag')

# 收集数据
color_msgs = {}
depth_msgs = {}
camera_info = None

for topic, msg, t in bag.read_messages(topics=['/camera/color/image_raw', '/camera/depth/points', '/camera/color/camera_info']):
    timestamp = t.to_sec()
    if topic == '/camera/color/image_raw':
        color_msgs[timestamp] = msg
    elif topic == '/camera/depth/points':
        depth_msgs[timestamp] = msg
    elif topic == '/camera/color/camera_info' and camera_info is None:
        camera_info = msg

# 处理第一帧
if color_msgs and depth_msgs and camera_info:
    depth_time = sorted(depth_msgs.keys())[0]
    closest_color_time = min(color_msgs.keys(), key=lambda x: abs(x - depth_time))
    
    pcd, color_img = create_colored_pointcloud_with_intrinsics(
        depth_msgs[depth_time], 
        color_msgs[closest_color_time], 
        camera_info
    )
    
    if pcd:
        coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.1)
        o3d.visualization.draw_geometries([pcd, coordinate_frame])

bag.close()
