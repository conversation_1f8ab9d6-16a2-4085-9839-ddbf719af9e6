import rosbag
import numpy as np
from sensor_msgs.msg import PointCloud2
import sensor_msgs.point_cloud2 as pc2
import open3d as o3d

bag = rosbag.Bag('my_recording.bag')

print("Reading point cloud data...")
message_count = 0

for topic, msg, t in bag.read_messages(topics=['/camera/depth/points']):
    message_count += 1
    print(f"Found message {message_count} on topic: {topic}")
    print(f"Message type: {type(msg)}")
    
    # 检查消息类型名称而不是类型本身
    if 'PointCloud2' in str(type(msg)):
        print("Processing PointCloud2 message...")
        # 转换为numpy数组
        points = pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True)
        points_array = np.array(list(points))
        
        print(f"Point cloud shape: {points_array.shape}")
        
        if points_array.size > 0:
            print("Creating Open3D point cloud...")
            # 创建Open3D点云对象
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points_array)
            
            # 可视化
            print("Visualizing point cloud...")
            o3d.visualization.draw_geometries([pcd])
        else:
            print("Point cloud is empty!")
        
        # 只处理第一帧
        break
    else:
        print(f"Message is not PointCloud2: {type(msg)}")

if message_count == 0:
    print("No messages found on topic /camera/depth/points")

bag.close()
print("Done.")
