import rosbag
import numpy as np
from sensor_msgs.msg import PointCloud2, Image
import sensor_msgs.point_cloud2 as pc2
from cv_bridge import CvBridge
import cv2
import open3d as o3d

bag = rosbag.Bag('my_recording.bag')
bridge = CvBridge()

print("Reading synchronized data...")

# 收集同一时间戳的数据
color_msgs = {}
depth_msgs = {}

# 先收集所有消息
for topic, msg, t in bag.read_messages(topics=['/camera/color/image_raw', '/camera/depth/points']):
    timestamp = t.to_sec()
    if topic == '/camera/color/image_raw':
        color_msgs[timestamp] = msg
    elif topic == '/camera/depth/points':
        depth_msgs[timestamp] = msg

# 找到最接近的时间戳
color_times = sorted(color_msgs.keys())
depth_times = sorted(depth_msgs.keys())

if color_times and depth_times:
    # 取第一帧
    depth_time = depth_times[0]
    closest_color_time = min(color_times, key=lambda x: abs(x - depth_time))
    
    print(f"Using depth frame at {depth_time}, color frame at {closest_color_time}")
    
    # 处理彩色图像
    color_msg = color_msgs[closest_color_time]
    color_image = bridge.imgmsg_to_cv2(color_msg, "bgr8")
    
    # 显示彩色图像
    cv2.imshow("Color Image", color_image)
    cv2.waitKey(1000)  # 显示1秒
    
    # 处理点云
    depth_msg = depth_msgs[depth_time]
    if 'PointCloud2' in str(type(depth_msg)):
        points = pc2.read_points(depth_msg, field_names=("x", "y", "z"), skip_nans=True)
        points_array = np.array(list(points))
        
        if points_array.size > 0:
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points_array)
            
            coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(
                size=0.1, origin=[0, 0, 0]
            )
            
            print("Visualizing point cloud...")
            o3d.visualization.draw_geometries([pcd, coordinate_frame])

cv2.destroyAllWindows()
bag.close()