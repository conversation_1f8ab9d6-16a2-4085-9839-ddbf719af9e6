import rosbag
import numpy as np
from sensor_msgs.msg import PointCloud2
import sensor_msgs.point_cloud2 as pc2
import open3d as o3d

bag = rosbag.Bag('my_recording.bag')

print("Reading point cloud data...")
for topic, msg, t in bag.read_messages(topics=['/camera/depth/points']):
    if isinstance(msg, PointCloud2):
        # 转换为numpy数组
        points = pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True)
        points_array = np.array(list(points))
        
        print(f"Point cloud shape: {points_array.shape}")
        
        if points_array.size > 0:
            # 创建Open3D点云对象
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points_array)
            
            # 可视化
            print("Visualizing point cloud...")
            o3d.visualization.draw_geometries([pcd])
        
        # 只处理第一帧
        break

bag.close()