#!/usr/bin/env python3
"""
ROS2 bag点云数据读取和可视化脚本
读取指定的ROS2 bag文件中的PointCloud2消息，统计点数并使用Open3D显示
直接读取SQLite数据库，无需rosbag2_py依赖
"""

import numpy as np
import open3d as o3d
import sqlite3
import struct
import sys
import os

def read_pointcloud_from_ros2_bag(bag_path, topic_name="/livox/lidar"):
    """
    从ROS2 bag文件中读取点云数据（直接读取SQLite数据库）

    Args:
        bag_path: ROS2 bag文件夹路径
        topic_name: 点云话题名称

    Returns:
        list: 包含所有点云帧的列表
    """
    # 检查bag文件夹是否存在
    if not os.path.exists(bag_path):
        print(f"错误: bag文件夹不存在: {bag_path}")
        return []

    # 查找.db3文件
    db_files = [f for f in os.listdir(bag_path) if f.endswith('.db3')]
    if not db_files:
        print(f"错误: 在 {bag_path} 中未找到.db3文件")
        return []

    db_file = os.path.join(bag_path, db_files[0])
    print(f"读取数据库文件: {db_file}")

    try:
        # 连接SQLite数据库
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()

        # 查询话题信息
        cursor.execute("SELECT name, type FROM topics")
        topics = cursor.fetchall()

        print("可用话题:")
        topic_id = None
        for topic_info in topics:
            print(f"  - {topic_info[0]}: {topic_info[1]}")
            if topic_info[0] == topic_name:
                # 获取话题ID
                cursor.execute("SELECT id FROM topics WHERE name = ?", (topic_name,))
                result = cursor.fetchone()
                if result:
                    topic_id = result[0]

        if topic_id is None:
            print(f"错误: 未找到话题 {topic_name}")
            conn.close()
            return []

        print(f"\n找到目标话题: {topic_name} (ID: {topic_id})")

        # 查询消息数据
        cursor.execute("""
            SELECT timestamp, data
            FROM messages
            WHERE topic_id = ?
            ORDER BY timestamp
        """, (topic_id,))

        messages = cursor.fetchall()
        conn.close()

        print(f"找到 {len(messages)} 条消息")

        pointclouds = []

        for i, (timestamp, data) in enumerate(messages):
            print(f"处理第 {i+1}/{len(messages)} 条消息...")

            # 解析CDR格式的PointCloud2消息
            points = parse_cdr_pointcloud2(data)

            if points is not None and len(points) > 0:
                pointclouds.append({
                    'points': points,
                    'timestamp': timestamp,
                    'point_count': len(points)
                })
                print(f"  - 点数: {len(points)}")
                print(f"  - 时间戳: {timestamp}")
            else:
                print("  - 警告: 点云为空或解析失败")

        print(f"\n总共处理了 {len(messages)} 条消息，有效点云帧数: {len(pointclouds)}")
        return pointclouds

    except Exception as e:
        print(f"读取数据库时出错: {e}")
        return []

def parse_cdr_pointcloud2(cdr_data):
    """
    解析CDR格式的PointCloud2消息

    Args:
        cdr_data: CDR格式的二进制数据

    Returns:
        numpy.ndarray: 点坐标数组 (N, 3)
    """
    try:
        # CDR格式解析
        # 跳过CDR头部（通常是4字节）
        offset = 4

        # 解析Header
        # 跳过header部分，直接找到PointCloud2的核心数据
        # 这是一个简化的解析，可能需要根据实际数据调整

        # 查找点云数据的模式
        # 通常在CDR数据中会有特定的模式来标识PointCloud2字段

        # 尝试多种偏移量来找到正确的数据开始位置
        for start_offset in [100, 120, 140, 160, 180, 200, 220, 240]:
            if start_offset >= len(cdr_data):
                continue

            points = parse_pointcloud_data_at_offset(cdr_data, start_offset)
            if points is not None and len(points) > 100:  # 假设有效点云至少有100个点
                return points

        # 如果上述方法失败，尝试搜索特定的字段模式
        return search_and_parse_pointcloud_fields(cdr_data)

    except Exception as e:
        print(f"解析CDR数据时出错: {e}")
        return None

def parse_pointcloud_data_at_offset(cdr_data, offset):
    """
    在指定偏移量处尝试解析点云数据
    """
    try:
        # 假设点云数据格式：每个点包含x,y,z,intensity等字段
        # Livox激光雷达通常每个点32字节（x,y,z,intensity,tag,line等）
        point_size = 32  # 字节

        remaining_data = len(cdr_data) - offset
        if remaining_data < point_size:
            return None

        num_points = remaining_data // point_size
        if num_points < 10:  # 至少要有10个点才认为是有效的
            return None

        points = []
        for i in range(num_points):
            point_offset = offset + i * point_size
            if point_offset + 12 <= len(cdr_data):  # 确保有足够的数据读取x,y,z
                # 读取x, y, z坐标（假设是float32，小端序）
                x = struct.unpack('<f', cdr_data[point_offset:point_offset + 4])[0]
                y = struct.unpack('<f', cdr_data[point_offset + 4:point_offset + 8])[0]
                z = struct.unpack('<f', cdr_data[point_offset + 8:point_offset + 12])[0]

                # 检查坐标是否合理（过滤明显错误的值）
                if (not (np.isnan(x) or np.isnan(y) or np.isnan(z)) and
                    abs(x) < 1000 and abs(y) < 1000 and abs(z) < 1000):
                    points.append([x, y, z])

        return np.array(points, dtype=np.float32) if len(points) > 10 else None

    except Exception as e:
        return None

def search_and_parse_pointcloud_fields(cdr_data):
    """
    搜索并解析点云字段数据
    """
    try:
        # 尝试不同的点大小和偏移量
        for point_size in [16, 20, 24, 28, 32, 36, 40]:
            for offset in range(50, min(300, len(cdr_data) - 1000), 4):
                points = parse_pointcloud_data_at_offset_with_size(cdr_data, offset, point_size)
                if points is not None and len(points) > 100:
                    return points

        return None

    except Exception as e:
        return None

def parse_pointcloud_data_at_offset_with_size(cdr_data, offset, point_size):
    """
    使用指定的点大小解析数据
    """
    try:
        remaining_data = len(cdr_data) - offset
        num_points = remaining_data // point_size

        if num_points < 10:
            return None

        points = []
        for i in range(min(num_points, 50000)):  # 限制最大点数避免内存问题
            point_offset = offset + i * point_size
            if point_offset + 12 <= len(cdr_data):
                x = struct.unpack('<f', cdr_data[point_offset:point_offset + 4])[0]
                y = struct.unpack('<f', cdr_data[point_offset + 4:point_offset + 8])[0]
                z = struct.unpack('<f', cdr_data[point_offset + 8:point_offset + 12])[0]

                if (not (np.isnan(x) or np.isnan(y) or np.isnan(z)) and
                    abs(x) < 1000 and abs(y) < 1000 and abs(z) < 1000 and
                    not (x == 0 and y == 0 and z == 0)):  # 排除原点
                    points.append([x, y, z])

        # 检查点的分布是否合理
        if len(points) > 100:
            points_array = np.array(points)
            # 检查点云的标准差，合理的点云应该有一定的分布
            std_x, std_y, std_z = np.std(points_array, axis=0)
            if std_x > 0.1 and std_y > 0.1 and std_z > 0.1:
                return points_array

        return None

    except Exception as e:
        return None

def combine_all_pointclouds(pointclouds):
    """
    组合所有帧的点云数据

    Args:
        pointclouds: 点云帧列表

    Returns:
        numpy.ndarray: 组合后的点坐标数组
    """
    if not pointclouds:
        return None

    print("正在组合点云数据...")
    all_points = []

    for i, pc in enumerate(pointclouds):
        if i % 10 == 0:  # 每10帧显示一次进度
            print(f"  处理进度: {i+1}/{len(pointclouds)} 帧")

        if pc['points'] is not None and len(pc['points']) > 0:
            all_points.append(pc['points'])

    if not all_points:
        print("没有有效的点云数据")
        return None

    # 合并所有点云
    combined_points = np.vstack(all_points)
    print(f"组合完成，总点数: {len(combined_points):,}")

    return combined_points

def visualize_pointcloud_with_open3d(points, title="Point Cloud"):
    """
    使用Open3D可视化点云
    
    Args:
        points: 点坐标数组 (N, 3)
        title: 窗口标题
    """
    if points is None or len(points) == 0:
        print("错误: 点云数据为空")
        return
    
    print(f"创建Open3D点云对象，点数: {len(points)}")
    
    # 创建Open3D点云对象
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    
    # 添加颜色（可选）
    # 根据z坐标着色
    z_values = points[:, 2]
    z_min, z_max = z_values.min(), z_values.max()
    if z_max > z_min:
        # 归一化z值到[0,1]
        z_normalized = (z_values - z_min) / (z_max - z_min)
        # 创建颜色映射（蓝色到红色）
        colors = np.zeros((len(points), 3))
        colors[:, 0] = z_normalized  # 红色分量
        colors[:, 2] = 1 - z_normalized  # 蓝色分量
        pcd.colors = o3d.utility.Vector3dVector(colors)
    
    # 创建坐标系
    coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(
        size=1.0,  # 坐标轴长度
        origin=[0, 0, 0]  # 原点位置
    )
    
    # 可视化
    print("启动Open3D可视化...")
    print("操作提示:")
    print("  - 鼠标左键拖拽: 旋转视角")
    print("  - 鼠标右键拖拽: 平移视角") 
    print("  - 滚轮: 缩放")
    print("  - 按 'Q' 或关闭窗口退出")
    
    o3d.visualization.draw_geometries(
        [pcd, coordinate_frame],
        window_name=title,
        width=1024,
        height=768
    )

def main():
    """主函数"""
    # 指定bag文件路径
    bag_path = "ros2_laser/rosbag2_2025_07_25-15_04_42"
    topic_name = "/livox/lidar"
    
    print("=" * 60)
    print("ROS2 Bag点云数据读取和可视化")
    print("=" * 60)
    print(f"Bag文件路径: {bag_path}")
    print(f"目标话题: {topic_name}")
    print()
    
    # 读取点云数据
    pointclouds = read_pointcloud_from_ros2_bag(bag_path, topic_name)
    
    if not pointclouds:
        print("未读取到有效的点云数据")
        return
    
    # 显示统计信息
    print("\n" + "=" * 60)
    print("点云数据统计:")
    print("=" * 60)
    total_points = sum(pc['point_count'] for pc in pointclouds)
    print(f"总帧数: {len(pointclouds)}")
    print(f"总点数: {total_points:,}")
    print(f"平均每帧点数: {total_points // len(pointclouds):,}")

    # 显示每帧的详细信息
    for i, pc in enumerate(pointclouds[:5]):  # 只显示前5帧
        print(f"第 {i+1} 帧: {pc['point_count']:,} 点")

    if len(pointclouds) > 5:
        print(f"... (还有 {len(pointclouds) - 5} 帧)")

    # 询问用户选择显示方式
    print("\n" + "=" * 60)
    print("显示选项:")
    print("1. 显示单帧点云")
    print("2. 组合所有帧显示完整点云")
    print("=" * 60)

    while True:
        try:
            choice = input("请选择显示方式 (1 或 2): ").strip()

            if choice == "1":
                # 显示单帧点云
                print(f"\n可视化第一帧点云 (共 {pointclouds[0]['point_count']:,} 个点)...")
                visualize_pointcloud_with_open3d(
                    pointclouds[0]['points'],
                    f"ROS2 Point Cloud - Frame 1 ({pointclouds[0]['point_count']:,} points)"
                )

                # 询问是否查看更多帧
                if len(pointclouds) > 1:
                    while True:
                        try:
                            frame_choice = input(f"\n是否查看其他帧? (输入帧号 1-{len(pointclouds)}, 或 'q' 退出): ").strip()
                            if frame_choice.lower() == 'q':
                                break

                            frame_idx = int(frame_choice) - 1
                            if 0 <= frame_idx < len(pointclouds):
                                print(f"可视化第 {frame_idx + 1} 帧点云...")
                                visualize_pointcloud_with_open3d(
                                    pointclouds[frame_idx]['points'],
                                    f"ROS2 Point Cloud - Frame {frame_idx + 1} ({pointclouds[frame_idx]['point_count']:,} points)"
                                )
                            else:
                                print(f"无效的帧号，请输入 1-{len(pointclouds)}")
                        except ValueError:
                            print("请输入有效的数字或 'q'")
                        except KeyboardInterrupt:
                            break
                break

            elif choice == "2":
                # 组合所有帧显示
                print(f"\n组合所有 {len(pointclouds)} 帧点云数据...")
                combined_points = combine_all_pointclouds(pointclouds)

                if combined_points is not None:
                    print(f"组合后总点数: {len(combined_points):,}")
                    visualize_pointcloud_with_open3d(
                        combined_points,
                        f"ROS2 Combined Point Cloud ({len(combined_points):,} points from {len(pointclouds)} frames)"
                    )
                else:
                    print("组合点云失败")
                break

            else:
                print("请输入 1 或 2")

        except KeyboardInterrupt:
            break
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
