import rosbag
import numpy as np
import math

bag = rosbag.Bag('my_recording.bag')

for topic, msg, t in bag.read_messages(topics=['/camera/color/camera_info']):
    print(f"彩色相机分辨率: {msg.width} x {msg.height}")
    K = np.array(msg.K).reshape(3, 3)
    print(f"内参矩阵K: {K}")
    print(f"畸变参数D: {msg.D}")
    
    # 计算FOV
    fx, fy = K[0, 0], K[1, 1]
    cx, cy = K[0, 2], K[1, 2]
    
    # 水平FOV
    fov_x = 2 * math.atan(msg.width / (2 * fx)) * 180 / math.pi
    # 垂直FOV
    fov_y = 2 * math.atan(msg.height / (2 * fy)) * 180 / math.pi
    # 对角线FOV
    diagonal = math.sqrt(msg.width**2 + msg.height**2)
    f_diagonal = math.sqrt(fx**2 + fy**2)
    fov_diagonal = 2 * math.atan(diagonal / (2 * f_diagonal)) * 180 / math.pi
    
    print(f"水平FOV: {fov_x:.1f}°")
    print(f"垂直FOV: {fov_y:.1f}°")
    print(f"对角线FOV: {fov_diagonal:.1f}°")
    break

for topic, msg, t in bag.read_messages(topics=['/camera/depth/camera_info']):
    print(f"\n深度相机分辨率: {msg.width} x {msg.height}")
    K = np.array(msg.K).reshape(3, 3)
    print(f"内参矩阵K: {K}")
    
    # 计算深度相机FOV
    fx, fy = K[0, 0], K[1, 1]
    fov_x = 2 * math.atan(msg.width / (2 * fx)) * 180 / math.pi
    fov_y = 2 * math.atan(msg.height / (2 * fy)) * 180 / math.pi
    
    print(f"深度相机水平FOV: {fov_x:.1f}°")
    print(f"深度相机垂直FOV: {fov_y:.1f}°")
    break

bag.close()